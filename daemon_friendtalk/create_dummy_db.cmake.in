# CMake script to create dummy DatabaseORA_MMS.cpp for development
set(SOURCE_FILE "@CMAKE_CURRENT_SOURCE_DIR@/lib/DatabaseORA_MMS.cpp")
set(DUMMY_FILE "@CMAKE_CURRENT_BINARY_DIR@/DatabaseORA_MMS_dummy.cpp")

# Read the original file
file(READ ${SOURCE_FILE} CONTENT)

# Replace PL/SQL blocks with dummy implementations
string(REPLACE 
    "EXEC SQL EXECUTE\n    BEGIN\n\n    //proc_get_ftk_msg_v3(:telco_name, :cmms_id, :sender_key, :dst_addr, :user_key, :msg_body, :button_name, :button_url, :button, :img_path, :img_link, :res_method, :timeout, :ad_flag, :wide, :kko_img_url, :ot_sqlcode, :ot_sqlmsg);\n    proc_get_ftkup_msg_v2(:telco_name, :cmms_id, :var_type, :sender_key, :dst_addr,                          \n                          :res_method, :encoding_type, :chat_bubble_type, \n                          :chat_extra, :chat_event, :img_path, :img_link, :wide, :kko_img_url, :ad_flag, :ot_sqlcode, :ot_sqlmsg);\n    END;\n    END-EXEC;"
    "// Dummy implementation for development\n    ot_sqlcode = 0;\n    strcpy(ot_sqlmsg, \"SUCCESS\");\n    cmms_id = 12345; // dummy message ID"
    CONTENT "${CONTENT}")

string(REPLACE 
    "EXEC SQL EXECUTE\n\t\tBEGIN\n\t\t\tproc_set_ack(:cmms_id, :msg_id, :ack_code, :ack_text, :telco_id, :ot_sqlcode, :ot_sqlmsg);\n\t\tEND;\n\t\tEND-EXEC;"
    "// Dummy implementation for development\n\t\tot_sqlcode = 0;\n\t\tstrcpy(ot_sqlmsg, \"SUCCESS\");"
    CONTENT "${CONTENT}")

string(REPLACE 
    "EXEC SQL EXECUTE\n\t\tBEGIN\n\t\t\tproc_set_rpt_skb(:cmms_id, :msg_id, :dlv_date, :snd_numb, :rcv_numb, :res_code, :res_text, :telco_id, :res_type, :end_telco, :ot_sqlcode, :ot_sqlmsg, :ot_queue_sqlcode, :ot_queue_sqlmsg);\n\t\tEND;\n\t\tEND-EXEC;"
    "// Dummy implementation for development\n\t\tot_sqlcode = 0;\n\t\tstrcpy(ot_sqlmsg, \"SUCCESS\");\n\t\tot_queue_sqlcode = 0;\n\t\tstrcpy(ot_queue_sqlmsg, \"SUCCESS\");"
    CONTENT "${CONTENT}")

string(REPLACE 
    "EXEC SQL EXECUTE\n\t\t\tBEGIN\n\t\t\tproc_set_msg_retry_skb(:telco_name, :cmms_id, :txt_path, :ot_sqlcode, :ot_sqlmsg);\n\t\tEND;\n\t\tEND-EXEC;"
    "// Dummy implementation for development\n\t\tot_sqlcode = 0;\n\t\tstrcpy(ot_sqlmsg, \"SUCCESS\");"
    CONTENT "${CONTENT}")

# Write the modified content to dummy file
file(WRITE ${DUMMY_FILE} "${CONTENT}")

message(STATUS "Created dummy DatabaseORA_MMS.cpp at ${DUMMY_FILE}")
